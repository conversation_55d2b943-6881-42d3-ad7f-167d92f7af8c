.navbar_container {
  position: sticky;
  top: 0;
  background: #242526;
  padding: 10px 0;
  z-index: 555;
  color: #fff;
}
.navbar_container .logo {
  display: flex;
  align-items: center;
}
.logo .logo_a {
  display: flex;
}
.navbar_container .logo img {
  max-width: 50px;
}
.navbar_container .menu_btn {
  margin-left: 10px;
}
.navbar_container .top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}
.navbar_container .top .left {
  display: flex;
  gap: 50px;
  align-items: center;
}
.navbar_container .top .right {
  display: flex;
  align-items: center;
  gap: 20px;
}
.navbar_container .search {
  position: relative;
  display: flex;
  width: 100%;
  max-width: 400px;
  background: #3a3b3c;
  border-radius: 50px;
  align-items: center;
  padding-left: 10px;
  color: #fff;
  height: 40px;
}
.header_search_result {
  position: absolute;
  background: #3a3b3c;
  width: 100%;
  top: 120%;
  left: 0;
  border-radius: 5px;
  height: 250px;
  overflow-y: auto;
}
.header_search_result .no_reuslt {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.navbar_container .search input {
  width: 100%;
  height: 100%;
  padding: 0 10px;
  border: 0;
  outline: 0;
  background: transparent;
  font-size: 15px;
  color: inherit;
}
.navbar_container .mobile_search {
  display: none;
}
@media screen and (max-width: 680px) {
  .navbar_container .search {
    display: none;
  }
  .navbar_container .mobile_search {
    display: flex;
    margin-top: 20px;
    max-width: 100%;
  }
  .navbar_container .logo {
    flex-wrap: wrap;
  }
  .navbar_container .top {
    flex-wrap: wrap;
  }
}

/* search results */
.userResult:hover,
.gameResult:hover {
  background: #303030;
}
.userResult .title,
.gameResult .title {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.avatar_image {
  width: 40px;
  min-width: 40px;
  height: 40px;
  border-radius: 50px;
  object-fit: cover;
  object-position: center;
  -o-object-fit: cover;
  -o-object-position: center;
}
/* user */
.userResult {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 5px 10px;
  cursor: pointer;
}

.userResult .admin {
  background: rgb(109, 109, 109);
  padding: 1px 5px;
  font-size: 12px;
  border-radius: 5px;
}
.userResult .image {
  width: 40px;
  min-width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}
.image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
/* game */
.gameResult {
  display: flex;
  gap: 10px;
  padding: 5px 10px;
}
.gameResult .image {
  width: 40px;
  min-width: 40px;
  height: 70px;
  overflow: hidden;
}
.gameResult .image img {
  object-fit: contain;
}
