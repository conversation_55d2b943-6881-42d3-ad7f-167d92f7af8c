.container {
  max-width: 900px;
  margin: 0 auto;
}

.card {
  background-color: #242526;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.header {
  border-bottom: 2px solid #444;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  color: #61dafb;
}

.header p {
  color: #aaa;
}

section {
  margin-bottom: 30px;
}

h2 {
  margin-bottom: 12px;
  color: #fff;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 15px;
}

label {
  display: block;
  font-weight: bold;
  color: #ccc;
}

span {
  display: block;
  margin-top: 2px;
  color: #fff;
}

.text-red {
  color: #e57373;
  font-weight: bold;
}

.text-green {
  color: #81c784;
  font-weight: bold;
}
.btn_section {
  display: flex;
  gap: 20px;
}
.btn_section button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 20px;
}
.btn_section .pay-button {
  background-color: #2196f3; /* blue */
  color: white;
}
.btn_section .pay-button:hover {
  background-color: #1976d2;
}
.btn_section .print-button {
  background-color: #4caf50;
  color: white;
}
.btn_section .print-button:hover {
  background-color: #45a049;
}

/* Payment form modal overlay */
.payment-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.payment-modal {
  background-color: #242526;
  border-radius: 12px;
  padding: 30px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.payment-modal h3 {
  color: #61dafb;
  margin-top: 0;
  margin-bottom: 20px;
  text-align: center;
}

.payment-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.payment-actions button {
  padding: 12px 20px;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-payment-btn {
  background-color: #2196f3;
  color: white;
  flex-grow: 1;
  margin-right: 10px;
}

.confirm-payment-btn:hover {
  background-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.confirm-payment-btn:disabled {
  background-color: #64b5f6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancel-payment-btn {
  background-color: #f44336;
  color: white;
}

.cancel-payment-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.cancel-payment-btn:disabled {
  background-color: #e57373;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-container {
  text-align: center;
  padding: 40px 20px;
  background-color: #242526;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.retry-button {
  background-color: #2196f3;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 20px;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Print-specific styles */
@media print {
  .container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
  }

  .card {
    padding: 0;
    border-radius: 0;
  }

  .grid {
    page-break-inside: avoid;
  }

  section {
    page-break-inside: avoid;
  }

  .payment-overlay {
    display: none;
  }
}
