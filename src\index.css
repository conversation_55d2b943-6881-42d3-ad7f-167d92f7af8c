*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
:root {
  --bg_dark: #18191a;
  --dark-mode-color-text: #fff;
  --bg_light: #ffffff;
}
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--bg_dark);
  color: var(--dark-mode-color-text);
}

.wrapper {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
}

a {
  color: #fff;
  text-decoration: none;
}

/* Print styles */
@media print {
  body {
    background: white;
    color: black;
  }

  .navbar_container {
    display: none !important;
  }

  .btn_section {
    display: none !important;
  }

  .card {
    background-color: white !important;
    box-shadow: none !important;
    color: black !important;
  }

  .header h1 {
    color: #0066cc !important;
  }

  .header p {
    color: #333 !important;
  }

  h2,
  label,
  span {
    color: black !important;
  }

  .text-red {
    color: #cc0000 !important;
  }

  .text-green {
    color: #007700 !important;
  }
}
