.update-tank-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.update-tank-header h2 {
  color: #fff;
  margin: 0;
}

.update_tank_form_row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

@media (max-width: 1024px) {
  .update_tank_form_row {
    grid-template-columns: repeat(2, 1fr);
  }
  .update_tank_form_row .col {
    grid-column: unset !important;
  }
}

@media (max-width: 768px) {
  .update_tank_form_row {
    grid-template-columns: 1fr;
  }
  .update_tank_form_row .col {
    grid-column: unset !important;
  }
  
  .update-tank-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}

.update_tank_form_row .col {
  background: #242526;
  padding: 20px;
  min-height: 300px;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.span-2 {
  grid-column: span 2;
}

.span-3 {
  grid-column: span 3;
}

.row_span-2 {
  grid-row: span 2;
}

.span-4 {
  grid-column: span 4;
}

.col h4 {
  margin-bottom: 20px;
}

.update_tank_form_row .inputs {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.update_tank_form_row .inputs input,
.update_tank_form_row .inputs select {
  background: #3a3b3c;
  color: #fff;
  border: 0;
  padding: 10px;
  width: 100%;
  border-radius: 5px;
}

.update_tank_form_row .inputs input:disabled,
.update_tank_form_row .inputs select:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Loading spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background: #242526;
  border-radius: 8px;
  padding: 30px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error container */
.error-container {
  background: #242526;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
}

.error-container h3 {
  color: #f44336;
  margin-bottom: 15px;
}

.error-container p {
  margin-bottom: 20px;
  color: #fff;
}
